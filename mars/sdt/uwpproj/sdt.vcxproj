<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4E682610-CCDA-4B06-AEB2-D2B4DDA23ED7}</ProjectGuid>
    <Keyword>StaticLibrary</Keyword>
    <RootNamespace>sdt</RootNamespace>
    <DefaultLanguage>zh-CN</DefaultLanguage>
    <MinimumVisualStudioVersion>14.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <WindowsTargetPlatformVersion>10.0.10586.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.10240.0</WindowsTargetPlatformMinVersion>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <GenerateManifest>false</GenerateManifest>
    <OutDir>$(SolutionDir)\bin\lib\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</OutDir>
    <IntDir>$(SolutionDir)\bin\build\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
      <CompileAsWinRT>false</CompileAsWinRT>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NOMINMAX;_WINRT_DLL;WP8;BOOST_THREAD_WIN32_HAS_GET_TICK_COUNT_64;BOOST_NO_ANSI_APIS;UNICODE;WP_PLATFORM;WIN32;WIN32_PLATFORM;_WINSOCK_DEPRECATED_NO_WARNINGS;_CRT_SECURE_NO_WARNINGS;BOOST_LITTLE_ENDIAN;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <AdditionalOptions>/bigobj %(AdditionalOptions)</AdditionalOptions>
      <DisableSpecificWarnings>28204</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>..\..\..\;..\..\;..\;..\..\comm\UWP;..\..\comm;..\src\tools;..\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>..\..\comm\UWP\projdefUWP.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\sdt_logic.cc" />
    <ClCompile Include="..\src\activecheck\basechecker.cc" />
    <ClCompile Include="..\src\activecheck\dnschecker.cc" />
    <ClCompile Include="..\src\activecheck\httpchecker.cc" />
    <ClCompile Include="..\src\activecheck\pingchecker.cc" />
    <ClCompile Include="..\src\activecheck\tcpchecker.cc" />
    <ClCompile Include="..\src\checkimpl\dnsquery.cc" />
    <ClCompile Include="..\src\checkimpl\httpquery.cc" />
    <ClCompile Include="..\src\checkimpl\pingquery.cc" />
    <ClCompile Include="..\src\checkimpl\tcpquery.cc" />
    <ClCompile Include="..\src\sdt_core.cc" />
    <ClCompile Include="..\src\tools\netchecker_trafficmonitor.cc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\activecheck\basechecker.h" />
    <ClInclude Include="..\src\activecheck\dnschecker.h" />
    <ClInclude Include="..\src\activecheck\httpchecker.h" />
    <ClInclude Include="..\src\activecheck\pingchecker.h" />
    <ClInclude Include="..\src\activecheck\tcpchecker.h" />
    <ClInclude Include="..\src\checkimpl\dnsquery.h" />
    <ClInclude Include="..\src\checkimpl\httpquery.h" />
    <ClInclude Include="..\src\checkimpl\http_url_parser.h" />
    <ClInclude Include="..\src\checkimpl\pingquery.h" />
    <ClInclude Include="..\src\checkimpl\tcpquery.h" />
    <ClInclude Include="..\src\sdt_core.h" />
    <ClInclude Include="..\src\tools\netchecker_socketutils.hpp" />
    <ClInclude Include="..\src\tools\netchecker_trafficmonitor.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>