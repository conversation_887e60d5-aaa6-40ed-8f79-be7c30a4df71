/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/
#if !defined(BOOST_PREDEF_LIBRARY_STD_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_LIBRARY_STD_H
#define BOOST_PREDEF_LIBRARY_STD_H
#endif

#include <boost/predef/library/std/_prefix.h>

#include <boost/predef/library/std/cxx.h>
#include <boost/predef/library/std/dinkumware.h>
#include <boost/predef/library/std/libcomo.h>
#include <boost/predef/library/std/modena.h>
#include <boost/predef/library/std/msl.h>
#include <boost/predef/library/std/roguewave.h>
#include <boost/predef/library/std/sgi.h>
#include <boost/predef/library/std/stdcpp3.h>
#include <boost/predef/library/std/stlport.h>
#include <boost/predef/library/std/vacpp.h>

#endif
