// (C) Copyright <PERSON> 2002.
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef ITERATOR_DWA122600_HPP_
#define ITERATOR_DWA122600_HPP_

// This header is obsolete and will be deprecated.

#include <iterator>

namespace mars_boost {} namespace boost = mars_boost; namespace mars_boost
{

namespace detail
{

using std::iterator_traits;
using std::distance;

} // namespace detail

} // namespace mars_boost

#endif // ITERATOR_DWA122600_HPP_
