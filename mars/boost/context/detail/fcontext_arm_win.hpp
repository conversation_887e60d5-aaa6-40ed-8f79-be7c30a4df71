
//          Copyright Oliver <PERSON> 2009.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_CONTEXT_DETAIL_FCONTEXT_ARM_WIN_H
#define BOOST_CONTEXT_DETAIL_FCONTEXT_ARM_WIN_H

#include <cstddef>

#include <boost/config.hpp>
#include <boost/cstdint.hpp>

#include <boost/context/detail/config.hpp>

#ifdef BOOST_HAS_ABI_HEADERS
# include BOOST_ABI_PREFIX
#endif

namespace mars_boost {} namespace boost = mars_boost; namespace mars_boost {
namespace context {

extern "C" {

#define BOOST_CONTEXT_CALLDECL

struct stack_t
{
    void    *   sp;
    std::size_t size;
    void    *   limit;

    stack_t() :
        sp( 0), size( 0), limit( 0)
    {}
};

struct fp_t
{
    mars_boost::uint32_t     fc_freg[16];

    fp_t() :
        fc_freg()
    {}
};

struct fcontext_t
{
    mars_boost::uint32_t     fc_greg[11];
    stack_t             fc_stack;
    fp_t                fc_fp;
    mars_boost::uint32_t     fc_dealloc;

    fcontext_t() :
        fc_greg(),
        fc_stack(),
        fc_fp(),
        fc_dealloc( 0)
    {}
};

}

}}

#ifdef BOOST_HAS_ABI_HEADERS
# include BOOST_ABI_SUFFIX
#endif

#endif // BOOST_CONTEXT_DETAIL_FCONTEXT_ARM_WIN_H
