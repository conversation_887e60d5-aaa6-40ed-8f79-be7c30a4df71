//-----------------------------------------------------------------------------
// boost variant/apply_visitor.hpp header file
// See http://www.boost.org for updates, documentation, and revision history.
//-----------------------------------------------------------------------------
//
// Copyright (c) 2003
// <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See
// accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_VARIANT_APPLY_VISITOR_HPP
#define BOOST_VARIANT_APPLY_VISITOR_HPP

#include "boost/variant/detail/apply_visitor_unary.hpp"
#include "boost/variant/detail/apply_visitor_binary.hpp"
#include "boost/variant/detail/apply_visitor_delayed.hpp"

#endif // BOOST_VARIANT_APPLY_VISITOR_HPP
