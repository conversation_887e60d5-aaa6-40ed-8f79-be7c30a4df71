#ifndef BOOST_THREAD_EXPERIMENTAL_TASK_REGION_HPP
#define BOOST_THREAD_EXPERIMENTAL_TASK_REGION_HPP

//////////////////////////////////////////////////////////////////////////////
//
// (C) Copyright Vicente J. Botet Escriba 2014. Distributed under the Boost
// Software License, Version 1.0. (See accompanying file
// LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/thread for documentation.
//
//////////////////////////////////////////////////////////////////////////////

#include <boost/thread/experimental/parallel/v2/task_region.hpp>

#endif
