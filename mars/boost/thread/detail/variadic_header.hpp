// Copyright (C) 2013 <PERSON> Escriba
//
//  Distributed under the Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#include <boost/config.hpp>

//#if defined BOOST_NO_CXX11_VARIADIC_TEMPLATES

#include <boost/preprocessor/facilities/intercept.hpp>
#include <boost/preprocessor/repetition/enum_params.hpp>
#include <boost/preprocessor/repetition/repeat_from_to.hpp>

#ifndef BOOST_THREAD_MAX_ARGS
#define BOOST_THREAD_MAX_ARGS 9
#endif

//#endif

